tell application "System Events"
	set resultText to ""
	set appList to application processes
	
	repeat with appProc in appList
		set appName to name of appProc
		try
			repeat with w in windows of appProc
				set winTitle to name of w
				if winTitle is not "" then
					set resultText to resultText & appName & " — " & winTitle & linefeed
				end if
			end repeat
		end try
	end repeat
end tell

return resultText
